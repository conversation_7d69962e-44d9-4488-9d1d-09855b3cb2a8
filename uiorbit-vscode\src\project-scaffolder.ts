/**
 * UIOrbit Project Scaffolder - Auto-create projects like Lovable.dev
 * Creates complete React/Vue/Angular projects with modern tooling
 */

import * as vscode from 'vscode';

import * as fs from 'fs';

import * as path from 'path';

import { exec } from 'child_process';

import { promisify } from 'util';

const execAsync = promisify(exec);

export interface ProjectConfig {
    framework: 'react' | 'vue' | 'angular' | 'svelte' | 'next' | 'nuxt';
    styling: 'tailwind' | 'styled-components' | 'emotion' | 'scss' | 'css-modules';
    uiLibrary?: 'shadcn' | 'mui' | 'antd' | 'chakra' | 'mantine';
    typescript: boolean;
    features: string[];
}

export class ProjectScaffolder {
    private workspaceRoot: string;

    constructor(workspaceRoot: string) {
        this.workspaceRoot = workspaceRoot;
    }

    /**
     * Detect if current workspace has a project or needs scaffolding
     */
    async needsScaffolding(): Promise<boolean> {
        const packageJsonPath = path.join(this.workspaceRoot, 'package.json');
        return !fs.existsSync(packageJsonPath);
    }

    /**
     * Auto-detect best framework based on user prompt
     */
    detectFrameworkFromPrompt(prompt: string): ProjectConfig {
        const lowerPrompt = prompt.toLowerCase();
        
        let framework: ProjectConfig['framework'] = 'react'; // default
        let styling: ProjectConfig['styling'] = 'tailwind'; // default
        let uiLibrary: ProjectConfig['uiLibrary'] = 'shadcn'; // default
        
        // Framework detection
        if (lowerPrompt.includes('vue') || lowerPrompt.includes('nuxt')) {
            framework = lowerPrompt.includes('nuxt') ? 'nuxt' : 'vue';
        } else if (lowerPrompt.includes('angular')) {
            framework = 'angular';
        } else if (lowerPrompt.includes('svelte')) {
            framework = 'svelte';
        } else if (lowerPrompt.includes('next')) {
            framework = 'next';
        }

        // Styling detection
        if (lowerPrompt.includes('styled-components') || lowerPrompt.includes('styled components')) {
            styling = 'styled-components';
        } else if (lowerPrompt.includes('emotion')) {
            styling = 'emotion';
        } else if (lowerPrompt.includes('scss') || lowerPrompt.includes('sass')) {
            styling = 'scss';
        }

        // UI Library detection
        if (lowerPrompt.includes('material') || lowerPrompt.includes('mui')) {
            uiLibrary = 'mui';
        } else if (lowerPrompt.includes('ant design') || lowerPrompt.includes('antd')) {
            uiLibrary = 'antd';
        } else if (lowerPrompt.includes('chakra')) {
            uiLibrary = 'chakra';
        } else if (lowerPrompt.includes('mantine')) {
            uiLibrary = 'mantine';
        }

        return {
            framework,
            styling,
            uiLibrary,
            typescript: true, // Always use TypeScript for better DX
            features: this.extractFeatures(lowerPrompt)
        };
    }

    private extractFeatures(prompt: string): string[] {
        const features: string[] = [];
        
        if (prompt.includes('auth') || prompt.includes('login')) {
            features.push('authentication');
        }
        if (prompt.includes('payment') || prompt.includes('stripe')) {
            features.push('payments');
        }
        if (prompt.includes('dashboard')) {
            features.push('dashboard');
        }
        if (prompt.includes('chart') || prompt.includes('graph')) {
            features.push('charts');
        }
        if (prompt.includes('form')) {
            features.push('forms');
        }
        if (prompt.includes('table') || prompt.includes('data')) {
            features.push('data-table');
        }
        if (prompt.includes('animation')) {
            features.push('animations');
        }
        if (prompt.includes('responsive')) {
            features.push('responsive');
        }
        if (prompt.includes('dark mode')) {
            features.push('dark-mode');
        }
        
        return features;
    }

    /**
     * Create a new project with the specified configuration
     */
    async createProject(config: ProjectConfig, projectName: string = 'uiorbit-project'): Promise<boolean> {
        try {
            vscode.window.showInformationMessage(`🚀 Creating ${config.framework} project with ${config.styling} and ${config.uiLibrary}...`);

            // Create project based on framework
            await this.scaffoldFramework(config, projectName);
            
            // Install styling framework
            await this.setupStyling(config);
            
            // Install UI library
            if (config.uiLibrary) {
                await this.setupUILibrary(config);
            }
            
            // Setup additional features
            await this.setupFeatures(config);
            
            vscode.window.showInformationMessage(`✅ Project created successfully! Opening in new window...`);
            
            // Open the new project
            const projectPath = path.join(this.workspaceRoot, projectName);
            await vscode.commands.executeCommand('vscode.openFolder', vscode.Uri.file(projectPath), true);
            
            return true;
        } catch (error) {
            vscode.window.showErrorMessage(`❌ Failed to create project: ${error}`);
            return false;
        }
    }

    private async scaffoldFramework(config: ProjectConfig, projectName: string): Promise<void> {
        const commands: Record<ProjectConfig['framework'], string> = {
            'react': `npx create-react-app ${projectName} --template ${config.typescript ? 'typescript' : 'javascript'}`,
            'next': `npx create-next-app@latest ${projectName} --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"`,
            'vue': `npm create vue@latest ${projectName} -- --typescript --router --pinia --eslint`,
            'nuxt': `npx nuxi@latest init ${projectName}`,
            'angular': `npx @angular/cli new ${projectName} --routing --style=scss --strict`,
            'svelte': `npm create svelte@latest ${projectName}`
        };

        await execAsync(commands[config.framework], { cwd: this.workspaceRoot });
    }

    private async setupStyling(config: ProjectConfig): Promise<void> {
        const projectPath = path.join(this.workspaceRoot, 'uiorbit-project');
        
        switch (config.styling) {
            case 'tailwind':
                if (config.framework !== 'next') { // Next.js already has Tailwind
                    await execAsync('npm install -D tailwindcss postcss autoprefixer', { cwd: projectPath });
                    await execAsync('npx tailwindcss init -p', { cwd: projectPath });
                }
                break;
            case 'styled-components':
                await execAsync('npm install styled-components', { cwd: projectPath });
                if (config.typescript) {
                    await execAsync('npm install -D @types/styled-components', { cwd: projectPath });
                }
                break;
            case 'emotion':
                await execAsync('npm install @emotion/react @emotion/styled', { cwd: projectPath });
                break;
        }
    }

    private async setupUILibrary(config: ProjectConfig): Promise<void> {
        const projectPath = path.join(this.workspaceRoot, 'uiorbit-project');
        
        const commands: Record<NonNullable<ProjectConfig['uiLibrary']>, string> = {
            'shadcn': 'npx shadcn-ui@latest init',
            'mui': 'npm install @mui/material @emotion/react @emotion/styled',
            'antd': 'npm install antd',
            'chakra': 'npm install @chakra-ui/react @emotion/react @emotion/styled framer-motion',
            'mantine': 'npm install @mantine/core @mantine/hooks @mantine/notifications'
        };

        if (config.uiLibrary) {
            await execAsync(commands[config.uiLibrary], { cwd: projectPath });
        }
    }

    private async setupFeatures(config: ProjectConfig): Promise<void> {
        const projectPath = path.join(this.workspaceRoot, 'uiorbit-project');
        
        for (const feature of config.features) {
            switch (feature) {
                case 'authentication':
                    await execAsync('npm install next-auth', { cwd: projectPath });
                    break;
                case 'payments':
                    await execAsync('npm install stripe @stripe/stripe-js', { cwd: projectPath });
                    break;
                case 'charts':
                    await execAsync('npm install recharts', { cwd: projectPath });
                    break;
                case 'forms':
                    await execAsync('npm install react-hook-form @hookform/resolvers zod', { cwd: projectPath });
                    break;
                case 'animations':
                    await execAsync('npm install framer-motion', { cwd: projectPath });
                    break;
            }
        }
    }

    /**
     * Get project recommendations based on prompt
     */
    getProjectRecommendations(prompt: string): string {
        const config = this.detectFrameworkFromPrompt(prompt);
        
        return `🎯 **Recommended Project Setup:**
        
**Framework:** ${config.framework.toUpperCase()}
**Styling:** ${config.styling}
**UI Library:** ${config.uiLibrary}
**Features:** ${config.features.join(', ') || 'Basic setup'}

This setup is optimized for your request: "${prompt}"

Would you like me to create this project automatically?`;
    }
}
